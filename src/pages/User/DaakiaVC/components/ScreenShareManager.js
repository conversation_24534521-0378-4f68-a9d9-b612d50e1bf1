import { useEffect, useRef, useMemo } from 'react';
import { Track, RoomEvent } from 'livekit-client';
import { useTracks, isTrackReference } from '@livekit/components-react';
import { log } from '@livekit/components-core';
import { getLocalStorage } from '../utils/helper';
import { constants } from '../utils/constants';

/**
 * ScreenShareManager - Isolated component that handles all screen sharing logic
 * This prevents the main VideoConference component from re-rendering on screen share changes
 */
export function ScreenShareManager({
  isElectronApp,
  screenShareDisplayId,
  layoutContext,
  onScreenShareUpdate
}) {
  const lastAutoFocusedScreenShareTrack = useRef(null);

  // Get only screen share tracks
  const tracks = useTracks(
    [
      { source: Track.Source.ScreenShare, withPlaceholder: false },
    ],
    { updateOnlyOn: [RoomEvent.ActiveSpeakersChanged], onlySubscribed: false }
  );

  // Filter screen share tracks
  const screenShareTracks = useMemo(() => 
    tracks
      .filter(isTrackReference)
      .filter((track) => track.publication.source === Track.Source.ScreenShare),
    [tracks]
  );

  // Notify parent when screen share tracks change
  useEffect(() => {
    const isEnabled = screenShareTracks.some((track) => track.publication.isSubscribed);
    onScreenShareUpdate?.(isEnabled, screenShareTracks);
  }, [screenShareTracks, onScreenShareUpdate]);

  // Main screen share useEffect - handles auto-focus logic
  useEffect(() => {
    // If screen share tracks are published, and no pin is set explicitly, auto set the screen share.
    if (
      screenShareTracks.some((track) => track.publication.isSubscribed) &&
      lastAutoFocusedScreenShareTrack.current === null
    ) {
      const [firstScreenShareTrack] = screenShareTracks;
      log.debug("Auto set screen share focus:", {
        newScreenShareTrack: firstScreenShareTrack,
      });
      layoutContext.pin.dispatch?.({
        msg: "set_pin",
        trackReference: firstScreenShareTrack,
      });
      lastAutoFocusedScreenShareTrack.current = firstScreenShareTrack;
      
      if (isElectronApp) {
        const userChoice = getLocalStorage(constants.MEETING_USER_CHOICES);
        window?.electronAPI?.ipcRenderer?.send("minimize-main-window", {
          video: userChoice.video,
          audio: userChoice.audio,
          screenShareDisplayId,
        });
      }
    } else if (
      lastAutoFocusedScreenShareTrack.current &&
      !screenShareTracks.some(
        (track) =>
          track.publication.trackSid ===
          lastAutoFocusedScreenShareTrack.current?.publication?.trackSid
      )
    ) {
      log.debug("Auto clearing screen share focus.");
      layoutContext.pin.dispatch?.({ msg: "clear_pin" });
      lastAutoFocusedScreenShareTrack.current = null;
      if (isElectronApp) {
        window?.electronAPI?.ipcRenderer?.send("stop-annotation");
      }
    }
  }, [
    screenShareTracks,
    layoutContext.pin,
    isElectronApp,
    screenShareDisplayId,
  ]);

  // This component doesn't render anything - it's just for logic
  return null;
}
