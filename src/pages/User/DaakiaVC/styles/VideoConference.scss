.lk-form-control {
  min-height: 38px;
  overflow-y: auto; /* Enable scrolling if content exceeds height */
  resize: none; /* Disable resizing */
}
.lk-video-conference{
  height: 100svh;
  // height: calc(100svh - var(--lk-control-bar-height));
  &-inner{
    justify-content: space-between;
    overflow: hidden;
    height: 100%;
    // .lk-focus-layout-wrapper{
    //   height: 100%;
    // }
    .daakia-logo{
      position: absolute;
      margin: 0.2rem 1rem;
      margin-top: 0.5rem;
      top: 0.5rem;
      left: 0.5rem;
      z-index: 1;
      background: black;
      padding: 0.4rem 0.2rem;
      width: 5rem;
      height: 2.5rem;
      border-radius: 6px;
      &.hidden {
        display: none;
      }
    }
    // .lk-grid-layout-wrapper{
    //   height: calc(100% - (var(--lk-control-bar-height) + 1.6rem)) !important;
    // }
  }
}
.lk-show-chat{
  height: 98%;
  min-width: 300px;
  width: auto;
  border: none;
  background-color: #000 !important;
  border-radius: 10px;
  margin: 10px;
  margin-left: 0;
  overflow: hidden;
  .lk-chat {
    width: 440px;
    height: 100%;
    border: none;
    // transform: translateX(100%);
    animation: side-drawer 0.2s ease-in;
    .lk-chat-messages{
      background-color: black;
      // display: flex;
      // justify-content: flex-end;
    }
  }
}
.lk-chat{
  background-color: #000;
  .lk-message-body{
    max-height: 13rem;
    overflow-x: hidden;
    overflow-y: auto;
    &::-webkit-scrollbar{
      width: 0;
    }
  }
}
@keyframes side-drawer {
  0%{
    opacity: 0;
    transform: translateX(100%);
  }
  100%{
    opacity: 1;
    transform: translateX(0);
  }
}
.lk-chat-inner {
  padding: var(--lk-grid-gap);
  height: 100%;
  padding-left: 0;
  width: 300px;
  justify-content: space-between;
}
.lk-grid-layout-wrapper {
  display: flex;
  flex-direction: row;
}
.lk-chat-header {
  background-color: #000;
  border-radius: 10px 10px 0px 0px;
  display: flex;
  justify-content: flex-start;
  padding-left: 1.5rem;
  border-bottom: 2px solid #111;
}
.lk-chat-messages::-webkit-scrollbar {
  width: 0;
}
.lk-close-button {
  border-radius: 50%;
  width: 40px;
  height: 40px;
  font-size: 20px;
  padding: 0;
}
.lk-toast {
  width: auto;
  height: auto;
  background-color: #242424;
  z-index: 1100;
  .pwj-toast {
    width: auto;
    display: flex;
    flex-direction: column;
    align-items: center;

    > p {
      font-size: 1.5rem;
      margin: 0;
      @media screen and (max-width: 1000px) {
        font-size: 1.2rem;
      }
    }
    .pwj-name-card {
      width: 100%;
      margin: 1rem 0.5rem;
      gap: 0.5rem;
      justify-content: start;
      align-items: center;
      > span {
        font-size: 12px;
      }
    }
    > div {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      gap: 1rem;
      button:nth-of-type(1) {
        color: #fff;
        @media screen and (max-width: 1000px){
          padding: 0 8px;
        }
      }
      button:nth-of-type(2) {
        color: #fff;
        border: 1px solid #fff;
        @media screen and (max-width: 1000px){
          padding: 0 8px;
        }
      }
    }
  }
}

@media screen and (max-width: 450px) {
  .lk-video-conference-inner{
    position: relative;
    overflow: hidden;
  }
  .button-icon{
    border-radius: 8px !important;
  }
  .control-bar-button-icon{
    border-radius: 10px !important;
  }
  .lk-button-group{
    border-radius: 10px !important;
  }
  .lk-button-group-menu{
    display: none;
  }
  .lk-chat{
    height: 100svh;
  }
  .lk-toast{
    width: 60%;
    .pwj-toast{
      width: 100%;
      > p{
        font-size: 1rem;
      }
      .pwj-name-card{
        > span{
          font-size: 16px;
        }
      }
      > div{
        button{
          padding: 0 6px;
        }
      }
    } 
  }
}

.br-timer{
  width: fit-content;
  height: fit-content;
  position: absolute;
  z-index: 10;
  left: 45%;
  top: 4%;
  background-color: rgb(255 255 255 / 15%);
  padding: 10px 50px;
  font-size: 20px;
  border-radius: 5px;
  @media screen and (max-width: 450px){
    left: 40%;
    top: 2%;
    padding: 5px 20px;
    font-size: 16px;
  }
}
.breakout-room-loader{
  background-color: #242424;
  height: 100vh;
  width: 100vw;
  .loading-title{
    color: white;
    margin: 0;
  }
  .loading-description{
    color: white;
  }
}
.back-alert{
  &-mobile{
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 100;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(2px);
  }
  &-web{
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 100;
    background-color: #3d3d3d33;
    backdrop-filter: blur(2.5px);
    &-box{
      border: 2px solid white;
      padding: 2rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      background-color: #000000;
      backdrop-filter: blur(4px);
      border-radius: 10px;
    }
    button{
      width: 5rem;
      height: 2.2rem;
      border-radius: 4px;
    }
  }
  &-confirm{
    display: flex;
    gap: 1rem;
  }
}

.whiteboard-focus{
  padding: 0.5rem;
  padding-bottom: 0.2rem;
  @media screen and (max-width: 450px){
    flex-direction: column;
    gap: 0.5rem;
    .lk-carousel{
      height: auto;
      .ant-avatar{
        height: 50px;
        width: 50px;
        font-size: 1rem;
      }
    }
    .excalidraw-wrapper{
      margin: 0;
    }
  }
}
.lk-video-conference-electron{
  .lk-grid-layout-wrapper{
    margin-top: 1.8rem;
  }
  .daakia-logo-electron{
    top: 2rem;
    left: -0.3rem;
  }
  .lk-focus-layout-wrapper{
    margin-top: 2rem;
    height: 87.4svh !important;
  }
  .lk-grid-layout-wrapper{
    height: 87.4svh;
  }
}
.pip-layout-container{
  .lk-participant-media-video{
    transform: scaleX(-1) !important;
  }
  .lk-focus-layout{
    height: -webkit-fill-available;
    grid-template-columns: 1fr;
    grid-template-rows: 1fr;
    margin-top: 2rem;
    max-height: calc(100svh - 5rem);
  }
  .control-bar-container{
    display: flex;
    justify-content: center;
  }
}
.lk-carousel{
  padding: 0.2rem;
  min-width: 11rem;
  &::-webkit-scrollbar-track{
    background-color: #111;
  }
  &::-webkit-scrollbar-thumb{
    background-color: #798593;
    padding: 0.3rem;
    border-radius: 0.3rem;
  }
}



.react-joyride__overlay{
  height: auto !important;
}

.lk-video-conference {
  &.mobile-video-conference {
    display: flex;
    flex-direction: column;
    height: 100svh;
    width: 100%;

    .mobile-video-conference-upper-controlbar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 1.7rem 1rem;
      background-color: #121212;
      height: 3rem;
      width: 100%;
      border-bottom: 1px solid #242424;
    }

    .lk-video-conference-inner {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      position: relative;
    }
  }
}